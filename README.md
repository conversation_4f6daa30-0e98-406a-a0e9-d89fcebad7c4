## TODO

[ ] 数据安全问题？A: 都在本地，不存在数据泄漏的防线
[ ] 不用考虑规模化带来的问题，可以很灵活的定制很多功能
[ ] 一个可执行程序在本地，然后远程连接页面。
    .好处是可执行程序只需要有基础能力就可以了，然后前端的话可以灵活的给用户修改，用户不用重新下载
    .很多一部分是可以重复用的，减少了二次开发的成本。但是可以满足各种用户的需求
[ ] 安全问题，执行必须落记录

## 为什么不单独使用Augment

[ ] 有什么会罢工
[ ] 打字太慢了，希望支持语音
[ ] 不希望什么都是我去规划，我希望的效果是，我给AI描述一个功能，AI自己规划，然后AI帮我实现，还有一个AI代表我去当监工

<!-- figd_Ga5Ih8Q-at57zn3LsE7IO3oADLTA_YeoNrLnzNd4 -->

1. 展示图标，不要使用background-image属性，直接使用img标签
2. 所有的img标签，都要设置display: block
3. 生成dom的时候，class为节点名称

3. 增加校验机制，校验节点的大小，位置，文本的字体颜色和大小，节点的层级关系，覆盖关系


<!-- 实现这个figma链接到index12.html:
https://www.figma.com/design/mJ7a24qESpwJ5xUpZpuMpy/NCast---Podcast-App--Community-?node-id=48-130&t=5W3O0CesSDH4qZlP-4

步骤如下：

要给图片，让AI对页面有个大概得了解

自己判断dom之间的关系？

1. 先将这个链接下载为整体的图片，然后放到debug.html的左侧

2. 获取这个链接的节点信息

3. 获取这个链接的下载icon资源, 获取下载icon资源的方法是调用: get_figma_data,并且传入extractDownloadableNodes为true

4. 下载icon资源到images文件夹

5. 创建index9.html,注意点如下
 第一步：需要设置rem适配，示例代码如下：
 (function () {
  const designWidth = 1125; // 👉 设计稿宽度
  const baseRem = 100;      // 👉 设定 1rem = 100px，方便换算

  function setRootFontSize() {
    const html = document.documentElement;
    const clientWidth = html.clientWidth;

    // 让页面宽度和设计稿成等比缩放
    html.style.fontSize = (clientWidth / designWidth) * baseRem + 'px';
  }

  setRootFontSize();
  window.addEventListener('resize', setRootFontSize);
})();
其中，designWidth需要根据实际情况修改，获取figma的根节点的宽度

第二点：生成html代码的时候需要注意，如果涉及到图片，需要设计成一个div，然后background-image是这个图片，然后background-size: contain,background-repeat: no-repeat,background-position: center;
第三点：生成dom的时候，class为节点名称。
第四点：文本内容，不要设置width属性。
第五点：如果一个节点需要设置position: absolute，那么它的父节点，也需要设置position: relative。而且这个节点的位置是相对于它的父节点的
第六点：页面中除了UI稿的节点，不要增加任何其他节点。比如debug相关的节点

6. 将debug.html的右侧iframe的默认链接换为index9.html

完成后直接结束，不要打开浏览器 -->

一、已有的步骤
下载的图片需要压缩

二、单个页面交互 + 数据流设计环节
【注】：需要给AI提供图片，辅助它推断动效
. 合理推断交互
. 合理推断动画效果
. 数据mock在本地
. 评估滚动

每个设计稿写完之后就是设计交互

三、整体页面跳转串联，也是需要AI推断。如果是手机端APP，需要检查底部导航是否要封装成组件，并且配置好点击跳转


四、设计后端数据库，MongoDB
【注】：需要给AI提供图片，辅助它推断业务逻辑，业务逻辑是可以互相串联的

四、初始化后端项目，包括CICD