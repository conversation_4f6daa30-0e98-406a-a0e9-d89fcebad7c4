#!/usr/bin/env node

import express from "express";
import { config } from "dotenv";
import { resolve } from "path";
import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { registerTools, getToolsInfo } from "./tools/index.js";
import { debug } from "./lib/debug.js";

// 加载 .env
config({ path: resolve(process.cwd(), ".env") });

// 创建 MCP Server 实例
const server = new McpServer({
  name: "shell-executor",
  version: "1.0.0"
});

// 注册工具
registerTools(server);

// 启动 HTTP 服务
async function main() {
  debug.info("Starting MCP HTTP server...");

  const app = express();
  app.use(express.json());

  // MCP 的入口 HTTP POST
  app.post("/mcp", async (req, res) => {
    try {
      const { method, params = {}, id = 1 } = req.body;
      debug.info("Received MCP request:", { method, id });

      let result;

      if (method === "tools/list") {
        // 返回工具列表
        result = { tools: getToolsInfo() };
      } else if (method === "tools/call") {
        // 调用工具
        const { name, arguments: args } = params;
        if (!name) {
          throw new Error("Tool name is required");
        }

        // 查找并调用工具
        const toolsInfo = getToolsInfo();
        const toolInfo = toolsInfo.find(t => t.name === name);

        if (!toolInfo) {
          throw new Error(`Tool not found: ${name}`);
        }

        // 调用工具处理函数
        result = await toolInfo.handler(args || {});
      } else {
        throw new Error(`Unsupported method: ${method}`);
      }

      // 返回 JSON 响应
      res.json({
        jsonrpc: "2.0",
        result,
        id
      });

    } catch (err) {
      debug.error("MCP handle error", err);
      if (!res.headersSent) {
        res.status(500).json({
          jsonrpc: "2.0",
          error: {
            code: -32603,
            message: err instanceof Error ? err.message : "Internal server error",
          },
          id: req.body?.id || null,
        });
      }
    }
  });



  const port = process.env.PORT || 3000;
  app.listen(port, () => {
    debug.info(`MCP HTTP Server is running at http://localhost:${port}`);
  });
}

// 错误处理
process.on('uncaughtException', (error) => {
  debug.error("Uncaught Exception", error);
  process.exit(1);
});
process.on('unhandledRejection', (reason, promise) => {
  debug.error("Unhandled Rejection", { reason, promise });
  process.exit(1);
});

main().catch((error) => {
  debug.error("Failed to start server", error);
  process.exit(1);
});